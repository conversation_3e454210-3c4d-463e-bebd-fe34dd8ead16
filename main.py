#!/usr/bin/env python3
"""
Main entry point for MCP-based Technical Assistance Chatbot
Multi-product PDF documentation assistant with dynamic content loading
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt

from config import config, ConfigError
from mcp_server import MCPServer
from session_manager import SessionManager

# Initialize console and app
console = Console()
app = typer.Typer(help="Technical Assistance Chatbot with MCP Protocol")
logger = logging.getLogger(__name__)

class ChatbotCLI:
    """Main CLI interface for the chatbot"""
    
    def __init__(self):
        self.mcp_server: Optional[MCPServer] = None
        self.session_manager: Optional[SessionManager] = None
        self.current_product: Optional[str] = None
    
    async def initialize(self):
        """Initialize MCP server and session manager"""
        try:
            console.print("[bold blue]🚀 Initializing MCP-based Technical Assistant...[/bold blue]")
            
            # Initialize MCP Server
            self.mcp_server = MCPServer()
            await self.mcp_server.start()
            
            # Initialize Session Manager
            self.session_manager = SessionManager(self.mcp_server)
            
            console.print("[bold green]✅ MCP Server initialized successfully[/bold green]")
            logger.info("MCP Server and Session Manager initialized")
            
        except Exception as e:
            console.print(f"[bold red]❌ Initialization failed: {e}[/bold red]")
            logger.error(f"Initialization failed: {e}")
            raise
    
    async def select_product(self) -> str:
        """Product selection interface"""
        try:
            # Get available products
            products = self.mcp_server.get_available_products()
            
            if not products:
                console.print("[bold red]❌ No products found in sorgenti directory[/bold red]")
                return None
            
            # Display available products
            console.print("\n[bold cyan]📁 Available Products:[/bold cyan]")
            for i, product in enumerate(products, 1):
                console.print(f"  {i}. {product}")
            
            # Get user selection
            while True:
                try:
                    choice = Prompt.ask(
                        "\n[bold yellow]Select product number[/bold yellow]",
                        choices=[str(i) for i in range(1, len(products) + 1)]
                    )
                    selected_product = products[int(choice) - 1]
                    break
                except (ValueError, IndexError):
                    console.print("[red]Invalid selection. Please try again.[/red]")
            
            return selected_product
            
        except Exception as e:
            console.print(f"[bold red]❌ Error selecting product: {e}[/bold red]")
            logger.error(f"Product selection error: {e}")
            return None
    
    async def load_product_resources(self, product: str) -> bool:
        """Load product resources into MCP server"""
        try:
            console.print(f"\n[bold blue]📚 Loading {product} resources...[/bold blue]")
            
            # Register product resources
            resource_count = await self.mcp_server.register_product_resources(product)
            
            if resource_count > 0:
                console.print(f"[bold green]✅ {resource_count} documents registered for {product}[/bold green]")
                self.current_product = product
                return True
            else:
                console.print(f"[bold yellow]⚠️  No documents found for {product}[/bold yellow]")
                return False
                
        except Exception as e:
            console.print(f"[bold red]❌ Error loading product resources: {e}[/bold red]")
            logger.error(f"Resource loading error: {e}")
            return False
    
    async def chat_loop(self):
        """Main chat interaction loop"""
        console.print(f"\n[bold green]🤖 {self.current_product} Technical Assistant ready![/bold green]")
        console.print("[dim]Type 'quit', 'exit', or 'q' to end the session[/dim]")
        console.print("[dim]Type 'help' for available commands[/dim]\n")
        
        while True:
            try:
                # Get user input
                user_input = Prompt.ask("[bold cyan]You[/bold cyan]").strip()
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    console.print("[bold yellow]👋 Goodbye![/bold yellow]")
                    break
                
                if user_input.lower() == 'help':
                    self.show_help()
                    continue
                
                if user_input.lower() == 'clear':
                    console.clear()
                    continue
                
                if user_input.lower() == 'status':
                    await self.show_status()
                    continue
                
                if not user_input:
                    continue
                
                # Process query through session manager
                console.print("[dim]🔍 Processing query...[/dim]")
                
                response = await self.session_manager.process_query(
                    user_input, 
                    self.current_product
                )
                
                # Display response
                self.display_response(response)
                
            except KeyboardInterrupt:
                console.print("\n[bold yellow]👋 Session interrupted. Goodbye![/bold yellow]")
                break
            except Exception as e:
                console.print(f"[bold red]❌ Error processing query: {e}[/bold red]")
                logger.error(f"Query processing error: {e}")
    
    def display_response(self, response: dict):
        """Display formatted response"""
        try:
            # Extract response components
            answer = response.get('answer', 'No answer provided')
            citations = response.get('citations', [])
            context_info = response.get('context_info', {})
            
            # Display main answer
            console.print(f"\n[bold green]🤖 Assistant:[/bold green]")
            console.print(Panel(answer, border_style="green"))
            
            # Display citations if present
            if citations:
                console.print("\n[bold blue]📚 Sources:[/bold blue]")
                for citation in citations:
                    console.print(f"  • {citation}")
            
            # Display context info if in debug mode
            if context_info and logger.level <= logging.DEBUG:
                console.print(f"\n[dim]Context: {context_info}[/dim]")
                
        except Exception as e:
            console.print(f"[bold red]❌ Error displaying response: {e}[/bold red]")
            logger.error(f"Response display error: {e}")
    
    def show_help(self):
        """Display help information"""
        help_text = """
[bold cyan]Available Commands:[/bold cyan]
• [bold]help[/bold] - Show this help message
• [bold]clear[/bold] - Clear the screen
• [bold]status[/bold] - Show current session status
• [bold]quit/exit/q[/bold] - Exit the application

[bold cyan]Usage Tips:[/bold cyan]
• Ask technical questions about the selected product
• Use specific terminology for better results
• Follow-up questions maintain conversation context
• Citations are automatically provided when available
        """
        console.print(Panel(help_text, title="Help", border_style="blue"))
    
    async def show_status(self):
        """Display current session status"""
        try:
            status_info = await self.session_manager.get_status()
            
            status_text = f"""
[bold]Current Product:[/bold] {self.current_product}
[bold]Active Resources:[/bold] {status_info.get('resource_count', 0)}
[bold]Session Duration:[/bold] {status_info.get('session_duration', 'Unknown')}
[bold]Queries Processed:[/bold] {status_info.get('query_count', 0)}
[bold]Cache Entries:[/bold] {status_info.get('cache_entries', 0)}
            """
            
            console.print(Panel(status_text, title="Session Status", border_style="yellow"))
            
        except Exception as e:
            console.print(f"[bold red]❌ Error getting status: {e}[/bold red]")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.mcp_server:
                await self.mcp_server.stop()
            console.print("[dim]Resources cleaned up[/dim]")
        except Exception as e:
            logger.error(f"Cleanup error: {e}")

async def main_async():
    """Async main function"""
    cli = ChatbotCLI()
    
    try:
        # Initialize system
        await cli.initialize()
        
        # Product selection
        product = await cli.select_product()
        if not product:
            console.print("[bold red]❌ No product selected. Exiting.[/bold red]")
            return
        
        # Load product resources
        if not await cli.load_product_resources(product):
            console.print("[bold red]❌ Failed to load product resources. Exiting.[/bold red]")
            return
        
        # Start chat loop
        await cli.chat_loop()
        
    except ConfigError as e:
        console.print(f"[bold red]❌ Configuration Error: {e}[/bold red]")
        console.print("[dim]Please check your .env file and configuration[/dim]")
    except KeyboardInterrupt:
        console.print("\n[bold yellow]👋 Application interrupted[/bold yellow]")
    except Exception as e:
        console.print(f"[bold red]❌ Unexpected error: {e}[/bold red]")
        logger.error(f"Unexpected error: {e}")
    finally:
        await cli.cleanup()

@app.command()
def run():
    """Run the technical assistance chatbot"""
    try:
        # Display startup banner
        banner = Text("Technical Assistance Chatbot", style="bold blue")
        console.print(Panel(banner, subtitle="MCP-based Multi-Product Assistant"))

        # Run async main
        asyncio.run(main_async())

    except Exception as e:
        console.print(f"[bold red]❌ Failed to start application: {e}[/bold red]")
        sys.exit(1)

def main():
    """Main entry point - defaults to run command"""
    try:
        # If no command specified, default to run
        if len(sys.argv) == 1:
            # Display startup banner
            banner = Text("Technical Assistance Chatbot", style="bold blue")
            console.print(Panel(banner, subtitle="MCP-based Multi-Product Assistant"))

            # Run async main
            asyncio.run(main_async())
        else:
            # Use typer for command handling
            app()

    except Exception as e:
        console.print(f"[bold red]❌ Failed to start application: {e}[/bold red]")
        sys.exit(1)

if __name__ == "__main__":
    main()
