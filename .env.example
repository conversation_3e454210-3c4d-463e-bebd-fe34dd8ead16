# API Configuration
GOOGLE_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash-exp

# Paths Configuration - Customize these paths
SORGENTI_PATH=./sorgenti
CACHE_PATH=./cache  
LOG_PATH=./logs

# Performance Settings - Adjust based on your needs
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200
CACHE_TTL=3600

# Security Settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=.pdf
MAX_CONCURRENT_DOCS=10

# MCP Server Settings
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8080
MCP_TIMEOUT=30

# Gemini-specific Settings
GEMINI_TEMPERATURE=0.2
GEMINI_MAX_OUTPUT_TOKENS=2048
GEMINI_TOP_P=0.8
GEMINI_TOP_K=40

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
