"""
Configuration management for MCP-based technical assistance chatbot.
Loads and validates all environment variables with secure defaults.
"""

import os
import logging
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ConfigError(Exception):
    """Configuration error exception"""
    pass

class Config:
    """Configuration class with validation and secure defaults"""
    
    def __init__(self):
        self._validate_required_vars()
        self._setup_logging()
    
    # API Configuration
    @property
    def google_api_key(self) -> str:
        key = os.getenv('GOOGLE_API_KEY')
        if not key:
            raise ConfigError("GOOGLE_API_KEY not found in environment variables")
        return key
    
    @property
    def gemini_model(self) -> str:
        return os.getenv('GEMINI_MODEL', 'gemini-2.0-flash-exp')
    
    # Paths Configuration
    @property
    def sorgenti_path(self) -> Path:
        path = Path(os.getenv('SORGENTI_PATH', './sorgenti'))
        if not path.exists():
            raise ConfigError(f"Sorgenti path does not exist: {path}")
        return path
    
    @property
    def cache_path(self) -> Path:
        path = Path(os.getenv('CACHE_PATH', './cache'))
        path.mkdir(exist_ok=True)
        return path
    
    @property
    def log_path(self) -> Path:
        path = Path(os.getenv('LOG_PATH', './logs'))
        path.mkdir(exist_ok=True)
        return path
    
    # Performance Settings
    @property
    def max_context_tokens(self) -> int:
        return int(os.getenv('MAX_CONTEXT_TOKENS', '8000'))
    
    @property
    def chunk_size(self) -> int:
        return int(os.getenv('CHUNK_SIZE', '1000'))
    
    @property
    def overlap_size(self) -> int:
        return int(os.getenv('OVERLAP_SIZE', '200'))
    
    @property
    def cache_ttl(self) -> int:
        return int(os.getenv('CACHE_TTL', '3600'))
    
    # Security Settings
    @property
    def max_file_size_mb(self) -> int:
        return int(os.getenv('MAX_FILE_SIZE_MB', '50'))
    
    @property
    def allowed_extensions(self) -> list:
        extensions = os.getenv('ALLOWED_EXTENSIONS', '.pdf')
        return [ext.strip() for ext in extensions.split(',')]
    
    @property
    def max_concurrent_docs(self) -> int:
        return int(os.getenv('MAX_CONCURRENT_DOCS', '10'))
    
    # MCP Server Settings
    @property
    def mcp_server_host(self) -> str:
        return os.getenv('MCP_SERVER_HOST', 'localhost')
    
    @property
    def mcp_server_port(self) -> int:
        return int(os.getenv('MCP_SERVER_PORT', '8080'))
    
    @property
    def mcp_timeout(self) -> int:
        return int(os.getenv('MCP_TIMEOUT', '30'))
    
    # Gemini-specific Settings
    @property
    def gemini_temperature(self) -> float:
        return float(os.getenv('GEMINI_TEMPERATURE', '0.2'))
    
    @property
    def gemini_max_output_tokens(self) -> int:
        return int(os.getenv('GEMINI_MAX_OUTPUT_TOKENS', '2048'))
    
    @property
    def gemini_top_p(self) -> float:
        return float(os.getenv('GEMINI_TOP_P', '0.8'))
    
    @property
    def gemini_top_k(self) -> int:
        return int(os.getenv('GEMINI_TOP_K', '40'))
    
    # Logging Configuration
    @property
    def log_level(self) -> str:
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def log_format(self) -> str:
        return os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    @property
    def log_max_bytes(self) -> int:
        return int(os.getenv('LOG_MAX_BYTES', '10485760'))
    
    @property
    def log_backup_count(self) -> int:
        return int(os.getenv('LOG_BACKUP_COUNT', '5'))
    
    def _validate_required_vars(self):
        """Validate presence of required environment variables"""
        required_vars = ['GOOGLE_API_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ConfigError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_file = self.log_path / 'chatbot.log'
        
        # Create rotating file handler
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=self.log_max_bytes,
            backupCount=self.log_backup_count
        )
        
        # Create console handler
        console_handler = logging.StreamHandler()
        
        # Create formatter
        formatter = logging.Formatter(self.log_format)
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            handlers=[file_handler, console_handler]
        )
        
        # Suppress some verbose loggers
        logging.getLogger('httpx').setLevel(logging.WARNING)
        logging.getLogger('sentence_transformers').setLevel(logging.WARNING)

# Global config instance
config = Config()
