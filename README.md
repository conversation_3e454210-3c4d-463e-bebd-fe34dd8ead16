# MCP-based Technical Assistance Chatbot

A sophisticated multi-product technical assistance chatbot that uses the Model Context Protocol (MCP) approach with Google Gemini-2.0-Flash for intelligent document processing and query answering.

## 🚀 Features

- **MCP Architecture**: Uses Model Context Protocol for efficient document management
- **Multi-Product Support**: Handle multiple product lines with separate documentation
- **Smart Citations**: Automatic citation management based on document categories
- **Semantic Search**: Advanced semantic search using sentence transformers
- **Conversation Continuity**: Maintains context across multiple queries
- **Security First**: Comprehensive input validation and rate limiting
- **Rich CLI Interface**: Beautiful command-line interface with Rich library

## 📋 Requirements

- Python 3.8 or higher
- Google AI Studio API key (for Gemini-2.0-Flash)
- PDF documents in the specified directory structure

## 🛠️ Installation

1. **Clone or download the project files**

2. **Run the setup script**:
   ```bash
   python setup.py
   ```

3. **Configure your API key**:
   - Edit the `.env` file created by setup
   - Replace `your_gemini_api_key_here` with your actual Google AI Studio API key

4. **Add your documents**:
   ```
   sorgenti/
   ├── SYM/
   │   ├── link/     # Documents requiring citations
   │   │   └── *.pdf
   │   └── nolink/   # Documents without citations
   │       └── *.pdf
   └── [OTHER_PRODUCTS]/
       ├── link/
       └── nolink/
   ```

## 🎯 Usage

### Start the Chatbot
```bash
python main.py run
```

### Run System Tests
```bash
python test_system.py
```

### Get Help
```bash
python main.py --help
```

## 📁 Project Structure

```
chatbot/
├── main.py                 # CLI entry point
├── config.py              # Configuration management
├── mcp_server.py          # MCP server core
├── mcp_tools.py           # MCP tools implementation
├── document_processor.py   # PDF processing and indexing
├── query_engine.py        # Query processing pipeline
├── session_manager.py     # Session and conversation management
├── security_utils.py      # Security and error handling
├── setup.py               # Installation setup script
├── test_system.py         # Comprehensive testing
├── requirements.txt       # Python dependencies
├── .env.example           # Environment template
└── README.md              # This file
```

## 🔧 Configuration

Key configuration options in `.env`:

```env
# API Configuration
GOOGLE_API_KEY=your_api_key_here
GEMINI_MODEL=gemini-2.0-flash-exp

# Performance Settings
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200

# Security Settings
MAX_FILE_SIZE_MB=50
MAX_CONCURRENT_DOCS=10
```

## 💬 Example Usage

```
Sistema: Prodotto SYM caricato. Come posso aiutarti?

Utente: Come avvito i bulloni della testa?
🤖 Assistant: I bulloni della testa vanno serrati con coppia di 1.8~2.2 Kg-m.

📚 Sources:
• Manuale officina Symphony ST 200 E5.pdf, pagina 4

Utente: E i prigionieri?
🤖 Assistant: I prigionieri testa/cilindro vanno serrati con coppia di 0.7~1.1 Kg-m 
e devono essere avvitati al carter motore.

📚 Sources:
• Manuale officina Symphony ST 200 E5.pdf, pagina 4
```

## 🏗️ Architecture

### MCP (Model Context Protocol) Approach

The system uses MCP instead of traditional RAG for several advantages:

- **Dynamic Loading**: Documents are loaded on-demand
- **Efficient Context Management**: Better memory usage
- **Real-time Processing**: No pre-indexing required
- **Flexible Resource Management**: Easy to add/remove documents

### Core Components

1. **MCP Server**: Manages resources, tools, and context
2. **Document Processor**: Extracts and indexes PDF content
3. **Query Engine**: Processes queries using MCP tools
4. **Session Manager**: Handles user sessions and continuity
5. **Security Layer**: Validates inputs and manages rate limits

### Citation Logic

- **`link/` documents**: Automatically include citations with filename and page
- **`nolink/` documents**: No citations added to responses
- **Smart Detection**: System automatically determines citation requirements

## 🔒 Security Features

- **Input Validation**: Comprehensive query and file path validation
- **Rate Limiting**: API and user request rate limiting
- **Safe Logging**: Sensitive data is never logged
- **Path Traversal Protection**: Prevents unauthorized file access
- **XSS Prevention**: Filters potentially dangerous content

## 🧪 Testing

The system includes comprehensive tests:

```bash
python test_system.py
```

Tests cover:
- Configuration loading
- MCP server initialization
- Document processing
- Security validations
- Query processing
- Citation logic
- Session management
- Error handling

## 📊 Performance

- **Response Time**: < 5 seconds for standard queries
- **Memory Usage**: Efficient caching and cleanup
- **Scalability**: Handles multiple concurrent sessions
- **Context Management**: Maintains conversation history

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**:
   - Ensure your Google AI Studio API key is correctly set in `.env`
   - Check that the API key has proper permissions

2. **No Documents Found**:
   - Verify PDF files are in the correct directory structure
   - Check file permissions and extensions

3. **Import Errors**:
   - Run `python setup.py` to install dependencies
   - Ensure Python 3.8+ is being used

4. **Memory Issues**:
   - Reduce `MAX_CONCURRENT_DOCS` in `.env`
   - Clear cache directory periodically

### Debug Mode

Enable debug logging by setting in `.env`:
```env
LOG_LEVEL=DEBUG
```

## 🤝 Contributing

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include security validations
4. Write tests for new features
5. Update documentation

## 📄 License

This project is provided as-is for educational and internal use purposes.

## 🙏 Acknowledgments

- Google AI Studio for Gemini API
- Sentence Transformers for semantic search
- Rich library for beautiful CLI interface
- PyMuPDF for PDF processing

---

**Note**: This chatbot is designed for technical documentation assistance. Always verify critical technical information from official sources.
