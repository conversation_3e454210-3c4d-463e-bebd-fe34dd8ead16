#!/usr/bin/env python3
"""
System Testing Script
Tests the complete MCP-based chatbot system with SYM product documents
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from config import config, ConfigError
from mcp_server import MCPServer
from session_manager import SessionManager
from security_utils import security_validator, rate_limiter, error_handler

# Configure logging for testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemTester:
    """Comprehensive system tester"""
    
    def __init__(self):
        self.mcp_server = None
        self.session_manager = None
        self.test_results = []
    
    async def run_all_tests(self):
        """Run all system tests"""
        logger.info("🚀 Starting comprehensive system tests...")
        
        try:
            # Test 1: Configuration and Setup
            await self.test_configuration()
            
            # Test 2: MCP Server Initialization
            await self.test_mcp_server_init()
            
            # Test 3: Document Processing
            await self.test_document_processing()
            
            # Test 4: Security Validations
            await self.test_security_features()
            
            # Test 5: Query Processing
            await self.test_query_processing()
            
            # Test 6: Citation Logic
            await self.test_citation_logic()
            
            # Test 7: Session Management
            await self.test_session_management()
            
            # Test 8: Error Handling
            await self.test_error_handling()
            
            # Generate test report
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            self.test_results.append({
                'test': 'Test Suite',
                'status': 'FAILED',
                'error': str(e)
            })
        finally:
            await self.cleanup()
    
    async def test_configuration(self):
        """Test configuration loading and validation"""
        logger.info("📋 Testing configuration...")
        
        try:
            # Test config loading
            assert config.sorgenti_path.exists(), "Sorgenti path should exist"
            assert config.cache_path.exists(), "Cache path should exist"
            assert config.log_path.exists(), "Log path should exist"
            
            # Test required settings
            assert config.google_api_key, "Google API key should be set"
            assert config.gemini_model, "Gemini model should be set"
            
            self.test_results.append({
                'test': 'Configuration',
                'status': 'PASSED',
                'details': 'All configuration values loaded correctly'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Configuration',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_mcp_server_init(self):
        """Test MCP server initialization"""
        logger.info("🖥️ Testing MCP server initialization...")
        
        try:
            self.mcp_server = MCPServer()
            await self.mcp_server.start()
            
            assert self.mcp_server.is_running, "MCP server should be running"
            
            # Test product discovery
            products = self.mcp_server.get_available_products()
            assert 'SYM' in products, "SYM product should be available"
            
            self.test_results.append({
                'test': 'MCP Server Initialization',
                'status': 'PASSED',
                'details': f'Server started, found {len(products)} products'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'MCP Server Initialization',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_document_processing(self):
        """Test document processing and resource registration"""
        logger.info("📚 Testing document processing...")
        
        try:
            if not self.mcp_server:
                raise Exception("MCP server not initialized")
            
            # Register SYM product resources
            resource_count = await self.mcp_server.register_product_resources('SYM')
            assert resource_count > 0, "Should register at least one resource"
            
            # Check resource metadata
            resources = self.mcp_server.resource_manager.get_resources_by_product('SYM')
            assert len(resources) == resource_count, "Resource count should match"
            
            # Verify resource structure
            for resource in resources[:2]:  # Test first 2 resources
                assert resource.name, "Resource should have name"
                assert resource.uri, "Resource should have URI"
                assert 'product' in resource.metadata, "Resource should have product metadata"
                assert 'category' in resource.metadata, "Resource should have category metadata"
            
            self.test_results.append({
                'test': 'Document Processing',
                'status': 'PASSED',
                'details': f'Processed {resource_count} documents for SYM'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Document Processing',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_security_features(self):
        """Test security validations"""
        logger.info("🔒 Testing security features...")
        
        try:
            # Test query validation
            valid_query = "Come avvito i bulloni della testa?"
            is_valid, error = security_validator.validate_query(valid_query)
            assert is_valid, f"Valid query should pass: {error}"
            
            # Test invalid query
            invalid_query = "<script>alert('xss')</script>"
            is_valid, error = security_validator.validate_query(invalid_query)
            assert not is_valid, "Invalid query should fail validation"
            
            # Test product validation
            is_valid, error = security_validator.validate_product_name('SYM')
            assert is_valid, f"Valid product should pass: {error}"
            
            # Test invalid product
            is_valid, error = security_validator.validate_product_name('../etc/passwd')
            assert not is_valid, "Invalid product should fail validation"
            
            # Test file path validation
            sym_pdf = config.sorgenti_path / 'SYM' / 'link' / 'Manuale officina Symphony ST 200 E5.pdf'
            if sym_pdf.exists():
                is_valid, error = security_validator.validate_file_path(sym_pdf)
                assert is_valid, f"Valid file path should pass: {error}"
            
            self.test_results.append({
                'test': 'Security Features',
                'status': 'PASSED',
                'details': 'All security validations working correctly'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Security Features',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_query_processing(self):
        """Test query processing pipeline"""
        logger.info("🔍 Testing query processing...")
        
        try:
            if not self.mcp_server:
                raise Exception("MCP server not initialized")
            
            # Initialize session manager
            self.session_manager = SessionManager(self.mcp_server)
            
            # Test basic query
            test_query = "Come avvito i bulloni della testa?"
            response = await self.session_manager.process_query(test_query, 'SYM')
            
            assert 'answer' in response, "Response should contain answer"
            assert isinstance(response['answer'], str), "Answer should be string"
            assert len(response['answer']) > 10, "Answer should be substantial"
            
            # Test document list query
            list_query = "Quali documenti sono disponibili?"
            response = await self.session_manager.process_query(list_query, 'SYM')
            
            assert 'answer' in response, "Response should contain answer"
            
            self.test_results.append({
                'test': 'Query Processing',
                'status': 'PASSED',
                'details': 'Query processing pipeline working correctly'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Query Processing',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_citation_logic(self):
        """Test citation logic for link vs nolink documents"""
        logger.info("📖 Testing citation logic...")
        
        try:
            if not self.session_manager:
                raise Exception("Session manager not initialized")
            
            # Test query that should return citations (from 'link' documents)
            citation_query = "Coppia di serraggio bulloni testa"
            response = await self.session_manager.process_query(citation_query, 'SYM')
            
            # Check if citations are present when they should be
            citations = response.get('citations', [])
            
            # Note: Citations will only appear if the query finds content from 'link' documents
            # This is expected behavior based on the citation logic
            
            self.test_results.append({
                'test': 'Citation Logic',
                'status': 'PASSED',
                'details': f'Citation logic working, found {len(citations)} citations'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Citation Logic',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_session_management(self):
        """Test session management features"""
        logger.info("👤 Testing session management...")
        
        try:
            if not self.session_manager:
                raise Exception("Session manager not initialized")
            
            # Test session status
            status = await self.session_manager.get_status()
            assert status.get('session_active'), "Session should be active"
            assert status.get('product') == 'SYM', "Session should be for SYM product"
            
            # Test conversation continuity
            first_query = "Come avvito i bulloni?"
            await self.session_manager.process_query(first_query, 'SYM')
            
            followup_query = "E i prigionieri?"
            response = await self.session_manager.process_query(followup_query, 'SYM')
            
            assert 'answer' in response, "Follow-up query should get response"
            
            self.test_results.append({
                'test': 'Session Management',
                'status': 'PASSED',
                'details': 'Session management working correctly'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Session Management',
                'status': 'FAILED',
                'error': str(e)
            })
    
    async def test_error_handling(self):
        """Test error handling mechanisms"""
        logger.info("⚠️ Testing error handling...")
        
        try:
            # Test invalid query handling
            if self.session_manager:
                invalid_response = await self.session_manager.process_query("", 'SYM')
                assert 'answer' in invalid_response, "Should handle empty query gracefully"
            
            # Test error handler
            test_error = ValueError("Test error")
            error_msg = error_handler.handle_processing_error(test_error, "test_context")
            assert isinstance(error_msg, str), "Error handler should return string"
            assert len(error_msg) > 0, "Error message should not be empty"
            
            self.test_results.append({
                'test': 'Error Handling',
                'status': 'PASSED',
                'details': 'Error handling mechanisms working correctly'
            })
            
        except Exception as e:
            self.test_results.append({
                'test': 'Error Handling',
                'status': 'FAILED',
                'error': str(e)
            })
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("📊 Generating test report...")
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASSED'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("🧪 SYSTEM TEST REPORT")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print("="*60)
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            print(f"{status_icon} {result['test']}: {result['status']}")
            
            if result['status'] == 'PASSED' and 'details' in result:
                print(f"   Details: {result['details']}")
            elif result['status'] == 'FAILED' and 'error' in result:
                print(f"   Error: {result['error']}")
        
        print("="*60)
        
        if failed_tests == 0:
            print("🎉 All tests passed! System is ready for use.")
        else:
            print(f"⚠️ {failed_tests} test(s) failed. Please review and fix issues.")
    
    async def cleanup(self):
        """Cleanup test resources"""
        try:
            if self.mcp_server:
                await self.mcp_server.stop()
            logger.info("🧹 Test cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

async def main():
    """Main test function"""
    try:
        # Check if .env file exists
        if not Path('.env').exists():
            print("❌ .env file not found. Please create it from .env.example and configure your API key.")
            return
        
        tester = SystemTester()
        await tester.run_all_tests()
        
    except ConfigError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please check your .env file and configuration.")
    except Exception as e:
        print(f"❌ Test suite failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
