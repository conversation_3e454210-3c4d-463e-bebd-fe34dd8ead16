"""
Security utilities and error handling
Implements comprehensive security validations, rate limiting, and safe logging
"""

import logging
import time
import hashlib
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import asyncio

from config import config

logger = logging.getLogger(__name__)

@dataclass
class RateLimitEntry:
    """Rate limiting entry for API calls"""
    count: int = 0
    window_start: datetime = field(default_factory=datetime.now)
    last_request: datetime = field(default_factory=datetime.now)

class SecurityValidator:
    """Validates inputs and enforces security policies"""
    
    def __init__(self):
        self.max_query_length = 1000
        self.max_filename_length = 255
        self.allowed_extensions = set(config.allowed_extensions)
        self.dangerous_patterns = [
            r'\.\./',  # Path traversal
            r'<script',  # XSS attempts
            r'javascript:',  # JavaScript injection
            r'file://',  # File protocol (except in controlled contexts)
            r'exec\(',  # Code execution attempts
            r'eval\(',  # Code evaluation attempts
        ]
    
    def validate_query(self, query: str) -> Tuple[bool, Optional[str]]:
        """Validate user query for security issues"""
        try:
            if not query or not query.strip():
                return False, "Query cannot be empty"
            
            if len(query) > self.max_query_length:
                return False, f"Query too long (max {self.max_query_length} characters)"
            
            # Check for dangerous patterns
            query_lower = query.lower()
            for pattern in self.dangerous_patterns:
                if re.search(pattern, query_lower):
                    logger.warning(f"Potentially dangerous pattern detected in query: {pattern}")
                    return False, "Query contains potentially unsafe content"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating query: {e}")
            return False, "Query validation failed"
    
    def validate_file_path(self, file_path: Path) -> Tuple[bool, Optional[str]]:
        """Validate file path for security"""
        try:
            # Convert to absolute path and resolve
            abs_path = file_path.resolve()
            
            # Check if path is within allowed directories
            sorgenti_path = config.sorgenti_path.resolve()
            
            try:
                abs_path.relative_to(sorgenti_path)
            except ValueError:
                return False, "File path outside allowed directory"
            
            # Check file extension
            if abs_path.suffix.lower() not in self.allowed_extensions:
                return False, f"File extension not allowed: {abs_path.suffix}"
            
            # Check filename length
            if len(abs_path.name) > self.max_filename_length:
                return False, "Filename too long"
            
            # Check if file exists and is readable
            if not abs_path.exists():
                return False, "File does not exist"
            
            if not abs_path.is_file():
                return False, "Path is not a file"
            
            # Check file size
            file_size = abs_path.stat().st_size
            max_size = config.max_file_size_mb * 1024 * 1024
            if file_size > max_size:
                return False, f"File too large: {file_size} bytes (max {max_size})"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating file path {file_path}: {e}")
            return False, "File path validation failed"
    
    def validate_product_name(self, product: str) -> Tuple[bool, Optional[str]]:
        """Validate product name"""
        try:
            if not product or not product.strip():
                return False, "Product name cannot be empty"
            
            # Check for path traversal attempts
            if '..' in product or '/' in product or '\\' in product:
                return False, "Invalid characters in product name"
            
            # Check length
            if len(product) > 50:
                return False, "Product name too long"
            
            # Check if product directory exists
            product_path = config.sorgenti_path / product
            if not product_path.exists() or not product_path.is_dir():
                return False, f"Product directory not found: {product}"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating product name {product}: {e}")
            return False, "Product name validation failed"
    
    def sanitize_log_message(self, message: str) -> str:
        """Sanitize log messages to prevent information leakage"""
        try:
            # Remove potential API keys (patterns like 'key=...', 'token=...', etc.)
            sanitized = re.sub(r'(key|token|password|secret)=[^\s&]+', r'\1=***', message, flags=re.IGNORECASE)
            
            # Remove file paths that might contain sensitive info
            sanitized = re.sub(r'/[^\s]*/(\.env|config|secret)', r'/***', sanitized)
            
            # Limit message length
            if len(sanitized) > 500:
                sanitized = sanitized[:497] + '...'
            
            return sanitized
            
        except Exception:
            return "Log message sanitization failed"

class RateLimiter:
    """Rate limiter for API calls and user requests"""
    
    def __init__(self):
        self.api_limits: Dict[str, RateLimitEntry] = {}
        self.user_limits: Dict[str, RateLimitEntry] = {}
        
        # Rate limits (requests per minute)
        self.api_rate_limit = 60  # Gemini API calls
        self.user_rate_limit = 30  # User queries
        
        # Cleanup interval
        self.last_cleanup = datetime.now()
        self.cleanup_interval = timedelta(minutes=5)
    
    async def check_api_rate_limit(self, api_key_hash: str) -> Tuple[bool, Optional[str]]:
        """Check if API rate limit is exceeded"""
        try:
            await self._cleanup_expired_entries()
            
            current_time = datetime.now()
            entry = self.api_limits.get(api_key_hash, RateLimitEntry())
            
            # Reset window if needed
            if current_time - entry.window_start > timedelta(minutes=1):
                entry.count = 0
                entry.window_start = current_time
            
            # Check limit
            if entry.count >= self.api_rate_limit:
                wait_time = 60 - (current_time - entry.window_start).seconds
                return False, f"API rate limit exceeded. Try again in {wait_time} seconds."
            
            # Update entry
            entry.count += 1
            entry.last_request = current_time
            self.api_limits[api_key_hash] = entry
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking API rate limit: {e}")
            return True, None  # Allow on error to avoid blocking
    
    async def check_user_rate_limit(self, user_id: str) -> Tuple[bool, Optional[str]]:
        """Check if user rate limit is exceeded"""
        try:
            await self._cleanup_expired_entries()
            
            current_time = datetime.now()
            entry = self.user_limits.get(user_id, RateLimitEntry())
            
            # Reset window if needed
            if current_time - entry.window_start > timedelta(minutes=1):
                entry.count = 0
                entry.window_start = current_time
            
            # Check limit
            if entry.count >= self.user_rate_limit:
                wait_time = 60 - (current_time - entry.window_start).seconds
                return False, f"Too many requests. Try again in {wait_time} seconds."
            
            # Update entry
            entry.count += 1
            entry.last_request = current_time
            self.user_limits[user_id] = entry
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking user rate limit: {e}")
            return True, None  # Allow on error to avoid blocking
    
    async def _cleanup_expired_entries(self):
        """Clean up expired rate limit entries"""
        try:
            current_time = datetime.now()
            
            if current_time - self.last_cleanup < self.cleanup_interval:
                return
            
            # Clean API limits
            expired_api_keys = [
                key for key, entry in self.api_limits.items()
                if current_time - entry.last_request > timedelta(hours=1)
            ]
            for key in expired_api_keys:
                del self.api_limits[key]
            
            # Clean user limits
            expired_user_keys = [
                key for key, entry in self.user_limits.items()
                if current_time - entry.last_request > timedelta(hours=1)
            ]
            for key in expired_user_keys:
                del self.user_limits[key]
            
            self.last_cleanup = current_time
            
            if expired_api_keys or expired_user_keys:
                logger.info(f"Cleaned up {len(expired_api_keys)} API and {len(expired_user_keys)} user rate limit entries")
                
        except Exception as e:
            logger.error(f"Error cleaning up rate limit entries: {e}")

class ErrorHandler:
    """Centralized error handling with safe logging"""
    
    def __init__(self):
        self.security_validator = SecurityValidator()
        self.error_counts: Dict[str, int] = {}
        self.last_error_reset = datetime.now()
    
    def handle_api_error(self, error: Exception, context: str = "") -> str:
        """Handle API errors safely"""
        try:
            error_type = type(error).__name__
            error_msg = str(error)
            
            # Sanitize error message
            safe_msg = self.security_validator.sanitize_log_message(error_msg)
            
            # Log error safely
            logger.error(f"API Error in {context}: {error_type} - {safe_msg}")
            
            # Track error frequency
            self._track_error(error_type)
            
            # Return user-friendly message
            if "quota" in error_msg.lower() or "rate limit" in error_msg.lower():
                return "API quota exceeded. Please try again later."
            elif "authentication" in error_msg.lower() or "api key" in error_msg.lower():
                return "Authentication error. Please check your API configuration."
            elif "timeout" in error_msg.lower():
                return "Request timed out. Please try again."
            else:
                return "An error occurred while processing your request. Please try again."
                
        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return "An unexpected error occurred."
    
    def handle_processing_error(self, error: Exception, context: str = "") -> str:
        """Handle processing errors safely"""
        try:
            error_type = type(error).__name__
            error_msg = str(error)
            
            # Sanitize error message
            safe_msg = self.security_validator.sanitize_log_message(error_msg)
            
            # Log error safely
            logger.error(f"Processing Error in {context}: {error_type} - {safe_msg}")
            
            # Track error frequency
            self._track_error(error_type)
            
            # Return user-friendly message based on error type
            if "file" in error_msg.lower() and "not found" in error_msg.lower():
                return "The requested document could not be found."
            elif "permission" in error_msg.lower() or "access" in error_msg.lower():
                return "Access denied to the requested resource."
            elif "memory" in error_msg.lower():
                return "Insufficient memory to process the request. Please try with a smaller query."
            else:
                return "An error occurred while processing the document. Please try again."
                
        except Exception as e:
            logger.error(f"Error in processing error handler: {e}")
            return "An unexpected processing error occurred."
    
    def _track_error(self, error_type: str):
        """Track error frequency for monitoring"""
        try:
            current_time = datetime.now()
            
            # Reset counts every hour
            if current_time - self.last_error_reset > timedelta(hours=1):
                self.error_counts.clear()
                self.last_error_reset = current_time
            
            # Increment error count
            self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
            
            # Log warning if error frequency is high
            if self.error_counts[error_type] > 10:
                logger.warning(f"High frequency of {error_type} errors: {self.error_counts[error_type]} in the last hour")
                
        except Exception as e:
            logger.error(f"Error tracking error frequency: {e}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics for monitoring"""
        try:
            return {
                'error_counts': self.error_counts.copy(),
                'last_reset': self.last_error_reset.isoformat(),
                'total_errors': sum(self.error_counts.values())
            }
        except Exception as e:
            logger.error(f"Error getting error stats: {e}")
            return {'error': str(e)}

# Global instances
security_validator = SecurityValidator()
rate_limiter = RateLimiter()
error_handler = ErrorHandler()

def get_api_key_hash() -> str:
    """Get hash of API key for rate limiting"""
    try:
        api_key = config.google_api_key
        return hashlib.sha256(api_key.encode()).hexdigest()[:16]
    except Exception:
        return "unknown"
