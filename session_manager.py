"""
Session Manager
Handles user sessions, conversation continuity, and query processing coordination
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Any

from mcp_server import MCPServer
from query_engine import QueryEngine, QueryResult
from security_utils import rate_limiter, error_handler

logger = logging.getLogger(__name__)

class SessionManager:
    """Manages user sessions and conversation continuity"""
    
    def __init__(self, mcp_server: MCPServer):
        self.mcp_server = mcp_server
        self.query_engine = QueryEngine(mcp_server)
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.current_session_id: Optional[str] = None
    
    async def create_session(self, product: str) -> str:
        """Create a new session for a product"""
        try:
            # Create session in MCP server
            session_id = self.mcp_server.create_session(product)
            
            # Initialize session data
            session_data = {
                'product': product,
                'created_at': datetime.now(),
                'last_activity': datetime.now(),
                'query_count': 0,
                'total_response_time': 0.0,
                'avg_response_time': 0.0
            }
            
            self.active_sessions[session_id] = session_data
            self.current_session_id = session_id
            
            logger.info(f"Created session {session_id} for product {product}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    async def process_query(self, query: str, product: str) -> Dict[str, Any]:
        """Process a user query and return formatted response"""
        try:
            # Check user rate limits
            user_id = f"session_{self.current_session_id or 'new'}"
            can_proceed, rate_error = await rate_limiter.check_user_rate_limit(user_id)
            if not can_proceed:
                logger.warning(f"User rate limit exceeded: {rate_error}")
                return {
                    'answer': rate_error,
                    'citations': [],
                    'context_info': {'rate_limit_error': rate_error}
                }

            # Ensure we have an active session
            if not self.current_session_id:
                self.current_session_id = await self.create_session(product)

            session_data = self.active_sessions.get(self.current_session_id)
            if not session_data:
                self.current_session_id = await self.create_session(product)
                session_data = self.active_sessions[self.current_session_id]

            # Update session activity
            start_time = time.time()
            session_data['last_activity'] = datetime.now()
            session_data['query_count'] += 1
            
            # Process query through query engine
            logger.info(f"Processing query in session {self.current_session_id}: '{query}'")
            
            # Update tool parameters with current session info
            query_result = await self.query_engine.process_query(
                query=query,
                product=product,
                session_id=self.current_session_id
            )
            
            # Update session statistics
            response_time = time.time() - start_time
            session_data['total_response_time'] += response_time
            session_data['avg_response_time'] = (
                session_data['total_response_time'] / session_data['query_count']
            )
            
            # Update conversation context in MCP server
            self.mcp_server.context_tracker.update_context(
                self.current_session_id,
                query,
                query_result.answer,
                query_result.citations
            )
            
            # Format response for CLI
            formatted_response = self._format_response(query_result, response_time)
            
            logger.info(f"Query processed successfully in {response_time:.2f}s")
            return formatted_response
            
        except Exception as e:
            error_msg = error_handler.handle_processing_error(e, "session_query_processing")
            return {
                'answer': error_msg,
                'citations': [],
                'context_info': {'error': 'processing_error'}
            }
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current session status"""
        try:
            if not self.current_session_id:
                return {
                    'session_active': False,
                    'message': 'No active session'
                }
            
            session_data = self.active_sessions.get(self.current_session_id)
            if not session_data:
                return {
                    'session_active': False,
                    'message': 'Session data not found'
                }
            
            # Get MCP server status
            server_status = self.mcp_server.get_server_status()
            
            # Calculate session duration
            duration = datetime.now() - session_data['created_at']
            
            # Get context info
            context = self.mcp_server.context_tracker.get_context(self.current_session_id)
            
            return {
                'session_active': True,
                'session_id': self.current_session_id,
                'product': session_data['product'],
                'session_duration': str(duration).split('.')[0],  # Remove microseconds
                'query_count': session_data['query_count'],
                'avg_response_time': f"{session_data['avg_response_time']:.2f}s",
                'last_activity': session_data['last_activity'].strftime('%H:%M:%S'),
                'resource_count': server_status['resource_count'],
                'cache_entries': server_status['cache_entries'],
                'conversation_history_length': len(context.conversation_history) if context else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting session status: {e}")
            return {
                'session_active': False,
                'error': str(e)
            }
    
    def _format_response(self, query_result: QueryResult, response_time: float) -> Dict[str, Any]:
        """Format query result for CLI display"""
        try:
            formatted = {
                'answer': query_result.answer,
                'citations': query_result.citations,
                'context_info': {
                    **query_result.context_info,
                    'response_time': f"{response_time:.2f}s",
                    'confidence': f"{query_result.confidence:.2f}" if query_result.confidence > 0 else None,
                    'tool_executions': len(query_result.tool_results)
                }
            }
            
            # Add debug information if available
            if logger.level <= logging.DEBUG:
                formatted['debug_info'] = {
                    'tool_results': [
                        {
                            'success': result.success,
                            'error': result.error,
                            'metadata': result.metadata
                        }
                        for result in query_result.tool_results
                    ]
                }
            
            return formatted
            
        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return {
                'answer': query_result.answer,
                'citations': query_result.citations,
                'context_info': {'formatting_error': str(e)}
            }
    
    async def cleanup_session(self, session_id: Optional[str] = None):
        """Clean up a specific session or current session"""
        try:
            target_session = session_id or self.current_session_id
            
            if target_session and target_session in self.active_sessions:
                del self.active_sessions[target_session]
                logger.info(f"Cleaned up session: {target_session}")
                
                if target_session == self.current_session_id:
                    self.current_session_id = None
                    
        except Exception as e:
            logger.error(f"Error cleaning up session: {e}")
    
    async def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """Clean up expired sessions"""
        try:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, session_data in self.active_sessions.items():
                age = current_time - session_data['created_at']
                if age > timedelta(hours=max_age_hours):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                await self.cleanup_session(session_id)
            
            if expired_sessions:
                logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
    
    def get_conversation_summary(self, session_id: Optional[str] = None) -> Optional[str]:
        """Get a summary of the conversation"""
        try:
            target_session = session_id or self.current_session_id
            if not target_session:
                return None
            
            context = self.mcp_server.context_tracker.get_context(target_session)
            if not context or not context.conversation_history:
                return "No conversation history available."
            
            # Build summary
            summary_parts = [
                f"Conversation Summary for {context.product}:",
                f"Started: {context.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
                f"Total exchanges: {len(context.conversation_history)}",
                ""
            ]
            
            # Add recent exchanges
            recent_exchanges = context.conversation_history[-3:]  # Last 3 exchanges
            for i, exchange in enumerate(recent_exchanges, 1):
                timestamp = exchange.get('timestamp', 'Unknown time')
                query = exchange.get('query', '')[:100] + ('...' if len(exchange.get('query', '')) > 100 else '')
                summary_parts.append(f"{i}. [{timestamp}] User: {query}")
            
            return '\n'.join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return f"Error generating summary: {str(e)}"
    
    async def switch_product(self, new_product: str) -> bool:
        """Switch to a different product"""
        try:
            # Clean up current session
            if self.current_session_id:
                await self.cleanup_session()
            
            # Create new session for new product
            self.current_session_id = await self.create_session(new_product)
            
            logger.info(f"Switched to product: {new_product}")
            return True
            
        except Exception as e:
            logger.error(f"Error switching to product {new_product}: {e}")
            return False
