"""
Document Processor for MCP Resources
Handles PDF processing, content extraction, metadata extraction, and indexing
"""

import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import hashlib

import fitz  # PyMuPDF
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

from config import config
from mcp_server import MCPResource
from security_utils import security_validator, error_handler

logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """Represents a chunk of document content"""
    text: str
    page_number: int
    chunk_index: int
    embedding: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = None

@dataclass
class ProcessedDocument:
    """Represents a fully processed document"""
    resource: MCPResource
    chunks: List[DocumentChunk]
    full_text: str
    page_count: int
    tables: List[Dict[str, Any]]
    images: List[Dict[str, Any]]
    
class PDFProcessor:
    """Handles PDF content extraction and processing"""
    
    def __init__(self):
        self.max_file_size = config.max_file_size_mb * 1024 * 1024
        self.chunk_size = config.chunk_size
        self.overlap_size = config.overlap_size
    
    def extract_content(self, pdf_path: Path) -> Tuple[str, List[Dict], List[Dict], int]:
        """Extract text, tables, images, and metadata from PDF"""
        try:
            # Validate file path
            is_valid, validation_error = security_validator.validate_file_path(pdf_path)
            if not is_valid:
                raise ValueError(f"File validation failed: {validation_error}")

            # Check file size
            if pdf_path.stat().st_size > self.max_file_size:
                raise ValueError(f"File too large: {pdf_path.stat().st_size} bytes")

            doc = fitz.open(pdf_path)
            full_text = ""
            tables = []
            images = []
            page_count = len(doc)
            
            for page_num in range(page_count):
                page = doc[page_num]
                
                # Extract text
                page_text = page.get_text()
                full_text += f"\n--- Page {page_num + 1} ---\n{page_text}"
                
                # Extract tables (basic implementation)
                tables.extend(self._extract_tables_from_page(page, page_num + 1))
                
                # Extract images with descriptions
                images.extend(self._extract_images_from_page(page, page_num + 1))
            
            doc.close()
            
            # Clean up text
            full_text = self._clean_text(full_text)
            
            logger.info(f"Extracted content from {pdf_path}: {len(full_text)} chars, {len(tables)} tables, {len(images)} images")
            
            return full_text, tables, images, page_count
            
        except Exception as e:
            logger.error(f"Error extracting content from {pdf_path}: {e}")
            raise
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[Dict]:
        """Extract tables from a page (basic implementation)"""
        tables = []
        try:
            # Look for table-like structures in text
            text = page.get_text()
            lines = text.split('\n')
            
            # Simple heuristic: lines with multiple tab/space separations
            table_lines = []
            for line in lines:
                if len(line.split()) > 3 and ('\t' in line or '  ' in line):
                    table_lines.append(line)
            
            if table_lines:
                tables.append({
                    'page': page_num,
                    'content': '\n'.join(table_lines),
                    'type': 'text_table'
                })
                
        except Exception as e:
            logger.warning(f"Error extracting tables from page {page_num}: {e}")
        
        return tables
    
    def _extract_images_from_page(self, page, page_num: int) -> List[Dict]:
        """Extract images and generate descriptions"""
        images = []
        try:
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                # Get image info
                xref = img[0]
                
                # Generate basic description based on context
                # In a real implementation, you might use OCR or image analysis
                description = f"Image {img_index + 1} on page {page_num}"
                
                images.append({
                    'page': page_num,
                    'index': img_index,
                    'description': description,
                    'xref': xref
                })
                
        except Exception as e:
            logger.warning(f"Error extracting images from page {page_num}: {e}")
        
        return images
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove excessive whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)
        
        # Remove page headers/footers (basic heuristic)
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # Skip very short lines that might be headers/footers
            if len(line) > 3 or line.startswith('---'):
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def create_chunks(self, text: str, page_mapping: Dict[int, int] = None) -> List[DocumentChunk]:
        """Create overlapping chunks from text"""
        chunks = []
        words = text.split()
        
        if not words:
            return chunks
        
        chunk_index = 0
        start_idx = 0
        
        while start_idx < len(words):
            # Determine chunk end
            end_idx = min(start_idx + self.chunk_size, len(words))
            
            # Create chunk text
            chunk_text = ' '.join(words[start_idx:end_idx])
            
            # Determine page number (simplified)
            page_number = self._estimate_page_number(chunk_text, start_idx, len(words))
            
            # Create chunk
            chunk = DocumentChunk(
                text=chunk_text,
                page_number=page_number,
                chunk_index=chunk_index,
                metadata={
                    'start_word': start_idx,
                    'end_word': end_idx,
                    'word_count': end_idx - start_idx
                }
            )
            
            chunks.append(chunk)
            
            # Move to next chunk with overlap
            start_idx = max(start_idx + self.chunk_size - self.overlap_size, start_idx + 1)
            chunk_index += 1
        
        logger.info(f"Created {len(chunks)} chunks from text")
        return chunks
    
    def _estimate_page_number(self, chunk_text: str, word_position: int, total_words: int) -> int:
        """Estimate page number for a chunk (simplified)"""
        # Look for page markers in text
        page_match = re.search(r'--- Page (\d+) ---', chunk_text)
        if page_match:
            return int(page_match.group(1))
        
        # Fallback: estimate based on position
        estimated_page = max(1, int((word_position / total_words) * 10))  # Assume ~10 pages average
        return estimated_page

class EmbeddingManager:
    """Manages document embeddings for semantic search"""
    
    def __init__(self):
        self.model = None
        self.index = None
        self.chunk_mapping = {}  # Maps index positions to chunks
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize sentence transformer model"""
        try:
            # Use a lightweight model for better performance
            model_name = "all-MiniLM-L6-v2"
            self.model = SentenceTransformer(model_name)
            logger.info(f"Initialized embedding model: {model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise
    
    def create_embeddings(self, chunks: List[DocumentChunk]) -> List[DocumentChunk]:
        """Create embeddings for document chunks"""
        try:
            if not chunks:
                return chunks
            
            # Extract texts
            texts = [chunk.text for chunk in chunks]
            
            # Generate embeddings
            embeddings = self.model.encode(texts, show_progress_bar=False)
            
            # Assign embeddings to chunks
            for chunk, embedding in zip(chunks, embeddings):
                chunk.embedding = embedding
            
            logger.info(f"Created embeddings for {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Error creating embeddings: {e}")
            return chunks
    
    def build_index(self, chunks: List[DocumentChunk]) -> bool:
        """Build FAISS index for semantic search"""
        try:
            if not chunks or not chunks[0].embedding is not None:
                logger.warning("No embeddings available for indexing")
                return False
            
            # Prepare embeddings matrix
            embeddings = np.array([chunk.embedding for chunk in chunks])
            dimension = embeddings.shape[1]
            
            # Create FAISS index
            self.index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)
            
            # Add to index
            self.index.add(embeddings)
            
            # Update chunk mapping
            self.chunk_mapping = {i: chunk for i, chunk in enumerate(chunks)}
            
            logger.info(f"Built FAISS index with {len(chunks)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Error building index: {e}")
            return False
    
    def search(self, query: str, k: int = 5) -> List[Tuple[DocumentChunk, float]]:
        """Search for similar chunks"""
        try:
            if not self.index or not self.model:
                return []
            
            # Generate query embedding
            query_embedding = self.model.encode([query])
            faiss.normalize_L2(query_embedding)
            
            # Search
            scores, indices = self.index.search(query_embedding, k)
            
            # Prepare results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx in self.chunk_mapping:
                    chunk = self.chunk_mapping[idx]
                    results.append((chunk, float(score)))
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching index: {e}")
            return []

class DocumentProcessor:
    """Main document processor coordinating PDF processing and indexing"""
    
    def __init__(self):
        self.pdf_processor = PDFProcessor()
        self.embedding_manager = EmbeddingManager()
        self.processed_docs: Dict[str, ProcessedDocument] = {}
    
    async def process_resource(self, resource: MCPResource) -> Optional[ProcessedDocument]:
        """Process a PDF resource into a searchable document"""
        try:
            pdf_path = Path(resource.metadata['file_path'])
            
            # Check if already processed (using file hash for cache key)
            cache_key = self._get_file_hash(pdf_path)
            if cache_key in self.processed_docs:
                logger.info(f"Using cached processed document: {resource.name}")
                return self.processed_docs[cache_key]
            
            logger.info(f"Processing document: {resource.name}")
            
            # Extract content
            full_text, tables, images, page_count = self.pdf_processor.extract_content(pdf_path)
            
            # Create chunks
            chunks = self.pdf_processor.create_chunks(full_text)
            
            # Create embeddings
            chunks = self.embedding_manager.create_embeddings(chunks)
            
            # Create processed document
            processed_doc = ProcessedDocument(
                resource=resource,
                chunks=chunks,
                full_text=full_text,
                page_count=page_count,
                tables=tables,
                images=images
            )
            
            # Cache processed document
            self.processed_docs[cache_key] = processed_doc
            
            # Update resource with content
            resource.content = full_text[:1000]  # Store preview
            resource.metadata.update({
                'page_count': page_count,
                'chunk_count': len(chunks),
                'table_count': len(tables),
                'image_count': len(images),
                'processed': True
            })
            
            logger.info(f"Successfully processed document: {resource.name}")
            return processed_doc
            
        except Exception as e:
            logger.error(f"Error processing resource {resource.name}: {e}")
            return None
    
    def _get_file_hash(self, file_path: Path) -> str:
        """Generate hash for file caching"""
        try:
            # Use file path and modification time for hash
            stat = file_path.stat()
            hash_input = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except Exception:
            return str(file_path)
    
    def search_documents(self, query: str, product: str = None) -> List[Dict[str, Any]]:
        """Search across processed documents"""
        try:
            all_results = []
            
            for processed_doc in self.processed_docs.values():
                # Filter by product if specified
                if product and processed_doc.resource.metadata.get('product') != product:
                    continue
                
                # Search in document chunks
                chunk_results = self.embedding_manager.search(query, k=3)
                
                for chunk, score in chunk_results:
                    all_results.append({
                        'resource_name': processed_doc.resource.name,
                        'chunk': chunk,
                        'score': score,
                        'page': chunk.page_number,
                        'citation_required': processed_doc.resource.metadata.get('citation_required', False),
                        'filename': processed_doc.resource.metadata.get('filename', ''),
                        'content': chunk.text
                    })
            
            # Sort by score
            all_results.sort(key=lambda x: x['score'], reverse=True)
            
            return all_results[:10]  # Return top 10 results
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    def get_document_content(self, resource_name: str, page_number: int = None) -> Optional[str]:
        """Get content from a specific document/page"""
        try:
            for processed_doc in self.processed_docs.values():
                if processed_doc.resource.name == resource_name:
                    if page_number:
                        # Return content from specific page
                        page_chunks = [
                            chunk for chunk in processed_doc.chunks 
                            if chunk.page_number == page_number
                        ]
                        return '\n'.join([chunk.text for chunk in page_chunks])
                    else:
                        # Return full document content
                        return processed_doc.full_text
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document content: {e}")
            return None
