"""
MCP Tools Implementation
Implements the three core MCP tools: search_documents, extract_content, and get_product_docs
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

from document_processor import DocumentProcessor
from mcp_server import MCPServer
from security_utils import security_validator, error_handler

logger = logging.getLogger(__name__)

@dataclass
class ToolResult:
    """Result from MCP tool execution"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None

class MCPToolHandler:
    """Handles execution of MCP tools"""
    
    def __init__(self, mcp_server: MCPServer):
        self.mcp_server = mcp_server
        self.document_processor = DocumentProcessor()
        self.processed_resources: Dict[str, bool] = {}
        
        # Register this handler with the MCP server
        self.mcp_server.tool_handler = self
    
    async def search_documents(self, query: str, product: str, context: str = "") -> ToolResult:
        """
        MCP Tool: Search documents for relevant content
        
        Args:
            query: Search query
            product: Product name to search within
            context: Previous conversation context
            
        Returns:
            ToolResult with search results
        """
        try:
            logger.info(f"Searching documents for query: '{query}' in product: {product}")
            
            # Ensure product resources are processed
            await self._ensure_product_processed(product)
            
            # Enhance query with context
            enhanced_query = self._enhance_query_with_context(query, context)
            
            # Search documents
            search_results = self.document_processor.search_documents(enhanced_query, product)
            
            if not search_results:
                return ToolResult(
                    success=True,
                    data=[],
                    metadata={'message': 'No relevant documents found'}
                )
            
            # Format results for MCP
            formatted_results = []
            for result in search_results:
                formatted_result = {
                    'resource_name': result['resource_name'],
                    'content': result['content'],
                    'page': result['page'],
                    'relevance_score': result['score'],
                    'citation_required': result['citation_required'],
                    'filename': result['filename']
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"Found {len(formatted_results)} relevant results")
            
            return ToolResult(
                success=True,
                data=formatted_results,
                metadata={
                    'query': query,
                    'enhanced_query': enhanced_query,
                    'product': product,
                    'result_count': len(formatted_results)
                }
            )
            
        except Exception as e:
            logger.error(f"Error in search_documents tool: {e}")
            return ToolResult(
                success=False,
                error=f"Search failed: {str(e)}"
            )
    
    async def extract_content(self, resource_uri: str, page_number: Optional[int] = None, 
                            section: Optional[str] = None) -> ToolResult:
        """
        MCP Tool: Extract specific content from a document
        
        Args:
            resource_uri: URI of the resource to extract from
            page_number: Specific page number (optional)
            section: Specific section name (optional)
            
        Returns:
            ToolResult with extracted content
        """
        try:
            logger.info(f"Extracting content from: {resource_uri}, page: {page_number}, section: {section}")
            
            # Parse resource name from URI
            resource_name = self._parse_resource_name_from_uri(resource_uri)
            if not resource_name:
                return ToolResult(
                    success=False,
                    error="Invalid resource URI format"
                )
            
            # Get resource from MCP server
            resource = self.mcp_server.resource_manager.get_resource(resource_name)
            if not resource:
                return ToolResult(
                    success=False,
                    error=f"Resource not found: {resource_name}"
                )
            
            # Ensure resource is processed
            await self._ensure_resource_processed(resource)
            
            # Extract content
            if page_number:
                content = self.document_processor.get_document_content(resource_name, page_number)
            else:
                content = self.document_processor.get_document_content(resource_name)
            
            if not content:
                return ToolResult(
                    success=False,
                    error="No content found for the specified criteria"
                )
            
            # Filter by section if specified
            if section:
                content = self._extract_section_content(content, section)
            
            return ToolResult(
                success=True,
                data={
                    'content': content,
                    'resource_name': resource_name,
                    'page_number': page_number,
                    'section': section,
                    'citation_required': resource.metadata.get('citation_required', False),
                    'filename': resource.metadata.get('filename', '')
                },
                metadata={
                    'extraction_type': 'page' if page_number else 'full',
                    'content_length': len(content)
                }
            )
            
        except Exception as e:
            logger.error(f"Error in extract_content tool: {e}")
            return ToolResult(
                success=False,
                error=f"Content extraction failed: {str(e)}"
            )
    
    async def get_product_docs(self, product: str) -> ToolResult:
        """
        MCP Tool: Get list of available documents for a product
        
        Args:
            product: Product name
            
        Returns:
            ToolResult with document list
        """
        try:
            logger.info(f"Getting document list for product: {product}")
            
            # Get resources for product
            resources = self.mcp_server.resource_manager.get_resources_by_product(product)
            
            if not resources:
                return ToolResult(
                    success=True,
                    data=[],
                    metadata={'message': f'No documents found for product: {product}'}
                )
            
            # Format document information
            doc_list = []
            for resource in resources:
                doc_info = {
                    'name': resource.name,
                    'filename': resource.metadata.get('filename', ''),
                    'category': resource.metadata.get('category', ''),
                    'citation_required': resource.metadata.get('citation_required', False),
                    'page_count': resource.metadata.get('page_count', 0),
                    'file_size': resource.metadata.get('file_size', 0),
                    'description': resource.description,
                    'last_accessed': resource.last_accessed.isoformat() if resource.last_accessed else None
                }
                doc_list.append(doc_info)
            
            # Sort by filename
            doc_list.sort(key=lambda x: x['filename'])
            
            logger.info(f"Found {len(doc_list)} documents for product {product}")
            
            return ToolResult(
                success=True,
                data=doc_list,
                metadata={
                    'product': product,
                    'document_count': len(doc_list),
                    'categories': list(set(doc['category'] for doc in doc_list))
                }
            )
            
        except Exception as e:
            logger.error(f"Error in get_product_docs tool: {e}")
            return ToolResult(
                success=False,
                error=f"Failed to get product documents: {str(e)}"
            )
    
    async def _ensure_product_processed(self, product: str):
        """Ensure all resources for a product are processed"""
        try:
            resources = self.mcp_server.resource_manager.get_resources_by_product(product)
            
            for resource in resources:
                if resource.name not in self.processed_resources:
                    await self._ensure_resource_processed(resource)
                    
        except Exception as e:
            logger.error(f"Error ensuring product processed: {e}")
    
    async def _ensure_resource_processed(self, resource):
        """Ensure a specific resource is processed"""
        try:
            if resource.name not in self.processed_resources:
                logger.info(f"Processing resource: {resource.name}")
                
                # Process the resource
                processed_doc = await self.document_processor.process_resource(resource)
                
                if processed_doc:
                    self.processed_resources[resource.name] = True
                    logger.info(f"Successfully processed resource: {resource.name}")
                else:
                    logger.warning(f"Failed to process resource: {resource.name}")
                    
        except Exception as e:
            logger.error(f"Error processing resource {resource.name}: {e}")
    
    def _enhance_query_with_context(self, query: str, context: str) -> str:
        """Enhance query with conversation context"""
        if not context:
            return query
        
        # Extract key terms from context
        context_words = context.lower().split()
        query_words = query.lower().split()
        
        # Add relevant context terms that aren't already in query
        enhanced_terms = []
        for word in context_words:
            if (len(word) > 3 and 
                word not in query_words and 
                word not in ['user:', 'assistant:', 'the', 'and', 'for', 'with']):
                enhanced_terms.append(word)
        
        if enhanced_terms:
            # Limit to most recent/relevant terms
            enhanced_terms = enhanced_terms[-5:]
            enhanced_query = f"{query} {' '.join(enhanced_terms)}"
            logger.debug(f"Enhanced query: '{query}' -> '{enhanced_query}'")
            return enhanced_query
        
        return query
    
    def _parse_resource_name_from_uri(self, uri: str) -> Optional[str]:
        """Parse resource name from URI"""
        try:
            # Expected format: file://path/to/sorgenti/product/category/filename.pdf
            # We want: product/category/filename_without_extension
            
            if not uri.startswith('file://'):
                return None
            
            file_path = uri[7:]  # Remove 'file://'
            path_parts = file_path.split('/')
            
            # Find sorgenti in path
            try:
                sorgenti_idx = path_parts.index('sorgenti')
                if len(path_parts) > sorgenti_idx + 3:
                    product = path_parts[sorgenti_idx + 1]
                    category = path_parts[sorgenti_idx + 2]
                    filename = path_parts[sorgenti_idx + 3]
                    
                    # Remove extension
                    filename_no_ext = filename.rsplit('.', 1)[0]
                    
                    return f"{product}/{category}/{filename_no_ext}"
            except ValueError:
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing resource name from URI {uri}: {e}")
            return None
    
    def _extract_section_content(self, content: str, section: str) -> str:
        """Extract specific section from content"""
        try:
            # Simple section extraction based on headers
            lines = content.split('\n')
            section_lines = []
            in_section = False
            
            section_lower = section.lower()
            
            for line in lines:
                line_lower = line.lower().strip()
                
                # Check if this line is a section header
                if (section_lower in line_lower and 
                    (line.strip().endswith(':') or len(line.strip()) < 50)):
                    in_section = True
                    section_lines.append(line)
                    continue
                
                # Check if we've hit a new section
                if (in_section and line.strip() and 
                    (line.strip().endswith(':') or 
                     (line.isupper() and len(line.strip()) < 50))):
                    # This might be a new section header
                    if section_lower not in line_lower:
                        break
                
                if in_section:
                    section_lines.append(line)
            
            if section_lines:
                return '\n'.join(section_lines)
            else:
                # Fallback: return content containing the section term
                relevant_lines = [
                    line for line in lines 
                    if section_lower in line.lower()
                ]
                return '\n'.join(relevant_lines) if relevant_lines else content
                
        except Exception as e:
            logger.error(f"Error extracting section '{section}': {e}")
            return content
