# Core dependencies for MCP-based chatbot
google-generativeai>=0.3.0
python-dotenv>=1.0.0
rich>=13.0.0
typer>=0.9.0
pymupdf>=1.23.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
numpy>=1.24.0
pandas>=2.0.0
python-magic>=0.4.27

# Additional dependencies for MCP implementation
asyncio-mqtt>=0.11.0
pydantic>=2.0.0
aiofiles>=23.0.0
httpx>=0.24.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
