#!/usr/bin/env python3
"""
Setup script for MCP-based Technical Assistance Chatbot
Helps users set up the environment and validate the installation
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def print_header(title):
    """Print formatted header"""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_step(step, description):
    """Print formatted step"""
    print(f"\n{step}. {description}")

def check_python_version():
    """Check Python version"""
    print_step(1, "Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print_step(2, "Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up environment configuration"""
    print_step(3, "Setting up environment...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file and add your Google API key")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def check_directories():
    """Check required directories"""
    print_step(4, "Checking directories...")
    
    required_dirs = ['sorgenti', 'cache', 'logs']
    all_exist = True
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ {dir_name}/ directory exists")
        else:
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"✅ Created {dir_name}/ directory")
            except Exception as e:
                print(f"❌ Failed to create {dir_name}/ directory: {e}")
                all_exist = False
    
    return all_exist

def check_sample_documents():
    """Check for sample documents"""
    print_step(5, "Checking sample documents...")
    
    sym_path = Path('sorgenti/SYM')
    if not sym_path.exists():
        print("⚠️  SYM product directory not found")
        print("Please ensure you have PDF documents in sorgenti/SYM/link/ and sorgenti/SYM/nolink/")
        return False
    
    link_docs = list((sym_path / 'link').glob('*.pdf')) if (sym_path / 'link').exists() else []
    nolink_docs = list((sym_path / 'nolink').glob('*.pdf')) if (sym_path / 'nolink').exists() else []
    
    total_docs = len(link_docs) + len(nolink_docs)
    
    if total_docs == 0:
        print("⚠️  No PDF documents found in SYM directories")
        print("Please add PDF documents to sorgenti/SYM/link/ and sorgenti/SYM/nolink/")
        return False
    
    print(f"✅ Found {len(link_docs)} documents in link/ and {len(nolink_docs)} in nolink/")
    return True

def validate_api_key():
    """Validate API key configuration"""
    print_step(6, "Validating API key...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    try:
        with open(env_file, 'r') as f:
            content = f.read()
        
        if 'GOOGLE_API_KEY=your_gemini_api_key_here' in content:
            print("⚠️  Please replace the placeholder API key in .env file")
            return False
        
        if 'GOOGLE_API_KEY=' not in content:
            print("❌ GOOGLE_API_KEY not found in .env file")
            return False
        
        print("✅ API key configuration found")
        return True
        
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def run_basic_test():
    """Run basic system test"""
    print_step(7, "Running basic system test...")
    
    try:
        # Import and test basic functionality
        from config import config
        print("✅ Configuration loading works")
        
        # Test that sorgenti path exists
        if config.sorgenti_path.exists():
            print("✅ Sorgenti path accessible")
        else:
            print("❌ Sorgenti path not accessible")
            return False
        
        print("✅ Basic system test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic system test failed: {e}")
        return False

def print_usage_instructions():
    """Print usage instructions"""
    print_header("USAGE INSTRUCTIONS")
    
    print("""
🚀 Your MCP-based Technical Assistance Chatbot is ready!

To start the chatbot:
    python main.py run

To run comprehensive tests:
    python test_system.py

To get help:
    python main.py --help

📚 Next Steps:
1. Make sure your Google API key is set in .env file
2. Add PDF documents to sorgenti/[PRODUCT]/link/ and sorgenti/[PRODUCT]/nolink/
3. Run the chatbot and select your product
4. Ask technical questions about your documents

📖 Documentation Structure:
- sorgenti/[PRODUCT]/link/     - Documents that require citations
- sorgenti/[PRODUCT]/nolink/   - Documents without citations
- cache/                       - Temporary processing cache
- logs/                        - Application logs

🔧 Configuration:
- Edit .env file to customize settings
- Check config.py for available options
- Logs are stored in logs/chatbot.log
    """)

def main():
    """Main setup function"""
    print_header("MCP TECHNICAL ASSISTANCE CHATBOT SETUP")
    
    success = True
    
    # Run setup steps
    success &= check_python_version()
    success &= install_dependencies()
    success &= setup_environment()
    success &= check_directories()
    success &= check_sample_documents()
    success &= validate_api_key()
    success &= run_basic_test()
    
    print_header("SETUP COMPLETE")
    
    if success:
        print("🎉 Setup completed successfully!")
        print_usage_instructions()
    else:
        print("⚠️  Setup completed with warnings.")
        print("Please review the issues above before running the chatbot.")
        print("\nYou can re-run this setup script at any time: python setup.py")

if __name__ == "__main__":
    main()
