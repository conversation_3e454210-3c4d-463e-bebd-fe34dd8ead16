# Prompt per Chatbot di Assistenza Tecnica Multi-Prodotto

## Obiettivo
Creare un chatbot CLI per assistenza tecnica che gestisce documentazione PDF multi-prodotto con caricamento dinamico dei contenuti e gestione intelligente delle citazioni.

## Architettura Consigliata
**Utilizzare approccio MCP (Model Context Protocol) invece di RAG** per i seguenti motivi:
- Gestione più efficiente del caricamento "al volo"
- Minore overhead computazionale
- Migliore gestione del contesto conversazionale
- Possibilità di processare direttamente i PDF senza pre-elaborazione

### Implementazione MCP Specifica

#### 1. MCP Server Setup
L'applicazione deve fungere da **MCP Server** che gestisce:
- **Resources**: I documenti PDF come risorse dinamiche
- **Tools**: Funzioni per ricerca e elaborazione contenuti
- **Prompts**: Template per query tecniche specifiche

#### 2. MCP Resources (Documenti PDF)
```python
# Struttura MCP Resource
{
    "name": f"{prodotto}/{categoria}/{filename}",
    "uri": f"file://sorgenti/{prodotto}/{categoria}/{filename}.pdf",
    "description": "Documento tecnico per assistenza",
    "metadata": {
        "product": prodotto,
        "citation_required": categoria == "link",
        "file_size": size_in_bytes,
        "pages": num_pages
    }
}
```

#### 3. MCP Tools Implementation
Implementare questi MCP tools:

**A. search_documents**
```python
{
    "name": "search_documents",
    "description": "Cerca contenuti nei documenti del prodotto",
    "parameters": {
        "query": "string",
        "product": "string", 
        "context": "string (conversazione precedente)"
    }
}
```

**B. extract_content**
```python
{
    "name": "extract_content",
    "description": "Estrae contenuto specifico da documento",
    "parameters": {
        "resource_uri": "string",
        "page_number": "integer (optional)",
        "section": "string (optional)"
    }
}
```

**C. get_product_docs**
```python
{
    "name": "get_product_docs", 
    "description": "Lista documenti disponibili per prodotto",
    "parameters": {
        "product": "string"
    }
}
```

#### 4. MCP Context Management
Il protocollo MCP gestisce automaticamente:
- **Context Window**: Mantiene finestra di contesto ottimale
- **Document State**: Traccia documenti attualmente caricati
- **Query History**: Storia query per continuità conversazionale
- **Resource Caching**: Cache intelligente delle risorse utilizzate

## Struttura del Sistema

### 1. Gestione Prodotti e Sorgenti
```
sorgenti/
├── SYM/
│   ├── nolink/
│   │   └── *.pdf (contenuti senza citazione fonte)
│   └── link/
│       └── *.pdf (contenuti con citazione fonte + pagina)
├── Infocar/
│   ├── nolink/
│   └── link/
└── ProdottoA/
    ├── nolink/
    └── link/
```

### 2. Componenti Principali da Implementare

#### A. MCP Server Core
- **Resource Manager**: Gestisce registrazione/deregistrazione documenti PDF
- **Tool Handler**: Esegue MCP tools (search, extract, list)
- **Context Tracker**: Mantiene stato conversazione e documenti attivi
- **Protocol Handler**: Gestisce comunicazione MCP con Gemini

#### B. Document Processor (MCP Resource)
- **PDF Parser**: Estrazione testo, tabelle e descrizioni immagini
- **Resource Registration**: Registra PDF come MCP resources
- **Metadata Extraction**: Estrae numero pagina, sezione, tipo contenuto
- **Content Indexing**: Indicizzazione per ricerca rapida

#### C. MCP Query Engine
- **Tool Execution**: Esegue MCP tools in base alle query
- **Context Integration**: Integra risultati nel contesto conversazionale  
- **Multi-Resource Search**: Cerca attraverso multiple risorse MCP
- **Response Synthesis**: Combina risultati da diverse fonti

#### D. MCP Response Generator
- **Answer Synthesis**: Utilizza risultati MCP tools per generare risposte
- **Citation Manager**: 
  - Resources "nolink": nessuna citazione  
  - Resources "link": cita fonte + pagina da metadata MCP
- **Technical Language**: Mantiene terminologia tecnica appropriata
- **Context Continuity**: Utilizza MCP context per domande successive

## Specifiche Tecniche

### 1. Gestione dei Contenuti
- **Formato Input**: PDF con testo, tabelle, immagini descrittive
- **Estrazione Dati**: Utilizza OCR avanzato per tabelle e immagini
- **Memoria Contestuale**: Mantiene ultimi 5-10 scambi conversazionali
- **Cache Intelligente**: Cache temporanea dei documenti processati per sessione

### 2. Logica di Ricerca
- **Semantic Search**: Ricerca semantica invece di keyword matching
- **Contextual Relevance**: Considera contesto conversazione precedente
- **Technical Terminology**: Gestisce sinonimi e varianti tecniche
- **Multi-document**: Cerca attraverso tutti i PDF del prodotto selezionato

### 3. Gestione Citazioni
```python
# Logica citazioni
if documento_origine == "nolink":
    response = generate_answer(content)  # Senza citazione
else:  # documento_origine == "link"
    response = generate_answer(content) + f"\n\nFonte: {nome_file}, pagina {numero_pagina}"
```

### 4. Continuità Conversazionale
- **Context Window**: Mantiene finestra di contesto 4000-8000 token
- **Topic Tracking**: Traccia argomento principale della conversazione
- **Reference Resolution**: Risolve pronomi e riferimenti impliciti
- **Follow-up Detection**: Riconosce domande di approfondimento

## Configurazione e Sicurezza

### Template .env.example
```env
# API Configuration
GOOGLE_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash

# Paths Configuration - Customize these paths
SORGENTI_PATH=./sorgenti
CACHE_PATH=./cache  
LOG_PATH=./logs

# Performance Settings - Adjust based on your needs
MAX_CONTEXT_TOKENS=8000
CHUNK_SIZE=1000
OVERLAP_SIZE=200
CACHE_TTL=3600

# Security Settings
MAX_FILE_SIZE_MB=50
ALLOWED_EXTENSIONS=.pdf
MAX_CONCURRENT_DOCS=10
```

### Config.py Implementation Requirements
```python
# Deve caricare tutte le variabili da .env
# Deve validare presenza API key
# Deve fornire defaults sicuri
# Deve gestire errori di configurazione
```

## Specifiche Gemini-2.0-Flash

### 1. Configurazione API
- **Endpoint**: Utilizza Google AI Studio API endpoint
- **Authentication**: API key da variabile ambiente
- **Rate Limiting**: Implementa backoff exponential per rate limits
- **Error Handling**: Gestisce timeout, quota exceeded, API errors

### 2. Prompt Engineering per Gemini
- **System Instructions**: Definire ruolo assistente tecnico specializzato
- **Context Management**: Ottimizza per finestra contesto Gemini (128k tokens)
- **Technical Language**: Istruzioni specifiche per terminologia tecnica automotive/meccanica
- **Structured Output**: Utilizza JSON mode quando disponibile per risposte strutturate

### 3. Ottimizzazioni Gemini-Specifiche
- **Batch Processing**: Raggruppa query multiple quando possibile
- **Context Caching**: Utilizza context caching per documenti frequenti
- **Streaming**: Implementa streaming responses per UX migliore
- **Temperature**: Usa temperature bassa (0.1-0.3) per risposte tecniche precise

## Implementazione Raccomandata

### 1. Stack Tecnologico
```txt
google-generativeai>=0.3.0
python-dotenv>=1.0.0
rich>=13.0.0
typer>=0.9.0
pymupdf>=1.23.0
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
numpy>=1.24.0
pandas>=2.0.0
python-magic>=0.4.27
```
- **Language Model**: Google Gemini-2.0-Flash per comprensione e generazione
- **PDF Processing**: PyMuPDF o pdfplumber per estrazione contenuti
- **Embedding**: Sentence-BERT per ricerca semantica
- **Vector Store**: FAISS o Chroma per ricerca veloce
- **CLI Framework**: Rich o Typer per interfaccia utente
- **Environment**: python-dotenv per gestione variabili ambiente

### 2. File Structure (Minimo)
```
chatbot/
├── main.py                 # Entry point CLI
├── mcp_server.py          # MCP Server implementation
├── mcp_tools.py           # MCP Tools (search, extract, list)
├── document_processor.py   # Processamento PDF come MCP Resources
├── session_manager.py      # Gestione sessione utente
├── config.py              # Configurazioni
├── .env                   # Variabili ambiente (API keys, paths)
├── .env.example           # Template per variabili ambiente
└── requirements.txt       # Dipendenze Python
```

### 1. Inizializzazione e Sicurezza
```python
# Esempio logica inizializzazione
import os
from dotenv import load_dotenv
import google.generativeai as genai

# Carica variabili ambiente
load_dotenv()

# Valida configurazione
if not os.getenv('GOOGLE_API_KEY'):
    raise ValueError("GOOGLE_API_KEY non trovata in .env")

# Configura Gemini
genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
model = genai.GenerativeModel(os.getenv('GEMINI_MODEL', 'gemini-2.0-flash-exp'))
```

### 2. Gestione Errori API
- **Rate Limiting**: Implementa retry con exponential backoff
- **Quota Management**: Monitora utilizzo quota e avvisa utente
- **Network Errors**: Gestisce timeout e connessioni perse
- **API Validation**: Valida risposte API prima del processing

### 3. Flusso Operativo MCP
1. **MCP Server Startup**: Avvia server MCP e registra tools disponibili
2. **Product Selection**: Richiede nome prodotto, registra PDF come MCP resources
3. **Resource Registration**: Ogni PDF diventa una MCP resource con metadata
4. **Query Processing**: 
   - Riceve domanda utente
   - Gemini utilizza MCP tools per cercare contenuti
   - MCP server esegue tools e ritorna risultati
   - Gemini genera risposta utilizzando context MCP
5. **Context Management**: MCP mantiene automaticamente stato conversazione

### 4. Esempio Implementazione MCP Tool
```python
async def search_documents(query: str, product: str, context: str = "") -> dict:
    """
    MCP Tool per ricerca documenti
    """
    # Carica resources del prodotto
    resources = get_product_resources(product)
    
    # Cerca nei documenti usando embedding
    results = []
    for resource in resources:
        content = extract_pdf_content(resource.uri)
        relevance = semantic_search(query + " " + context, content)
        if relevance > threshold:
            results.append({
                "resource": resource.name,
                "content": content,
                "page": extract_page_number(content),
                "citation_required": resource.metadata.get("citation_required", False)
            })
    
    return {
        "results": results,
        "context_updated": True
    }
```

### 4. Ottimizzazioni Performance MCP
- **Resource Lazy Loading**: Registra resources senza caricare contenuto finché non richiesto
- **Tool Caching**: Cache risultati MCP tools per query simili
- **Async Processing**: Esecuzione asincrona tools MCP per performance
- **Context Streaming**: Stream aggiornamenti context a Gemini in tempo reale
- **Resource Pooling**: Pool di connessioni per accesso documenti multipli

## Esempi di Comportamento Atteso

### Conversazione Tipo (con MCP):
```
Sistema: Avvio MCP Server...
Sistema: MCP Server attivo. Inserire nome prodotto: SYM
Sistema: Registrazione resources SYM... 15 documenti registrati
Sistema: Prodotto SYM caricato. Come posso aiutarti?

Utente: Come avvito i bulloni della testa?
[MCP: search_documents(query="bulloni testa", product="SYM")]
[MCP: extract_content(resource="SYM/link/manuale_officina.pdf", section="serraggi")]
Bot: I bulloni della testa vanno serrati con coppia di 1.8~2.2 Kg-m. 
Fonte: Manuale officina Symphony ST 200 E5.pdf, pagina 4

Utente: E i prigionieri?
[MCP: search_documents(query="prigionieri", product="SYM", context="bulloni testa serraggi")]
Bot: I prigionieri testa/cilindro vanno serrati con coppia di 0.7~1.1 Kg-m e devono essere avvitati al carter motore.
Fonte: Manuale officina Symphony ST 200 E5.pdf, pagina 4
```

## Criteri di Successo
- **Velocità**: Risposta in <5 secondi per query standard
- **Accuratezza**: >90% risposte tecnicamente corrette  
- **Contesto**: Mantiene continuità conversazionale per ≥5 scambi
- **Citazioni**: Applica correttamente regole nolink/link
- **Usabilità**: Interfaccia CLI intuitiva e responsive
- **Sicurezza**: API keys mai esposte nel codice o logs
- **Affidabilità**: Gestione robusta errori API e network

## Note Implementative
- **Security First**: Mai committare .env, sempre usare .env.example
- **Error Logging**: Log dettagliato errori senza esporre API keys
- **Graceful Degradation**: Fallback per contenuti non trovati o API non disponibile
- **Performance Monitoring**: Track utilizzo token e costi API
- **Data Validation**: Validare tutti input utente e contenuti PDF
- **Memory Management**: Pulire cache periodicamente per evitare memory leaks
- **API Best Practices**: Seguire rate limits e best practices Google AI